from typing import List, Dict, Any

books: List[Dict[str,Any]]= [
    {
        "id": 1,
        "title": "Think Python",
        "author": "<PERSON>",
        "publisher": "O'Reilly Media",
        "published_date": "2021-01-01",
        "page_count": 1234,
        "language": "English",
    },
    {
        "id": 2,
        "title": "Django By Example",
        "author": "<PERSON>",
        "publisher": "Packt Publishing Ltd",
        "published_date": "2022-01-19",
        "page_count": 1023,
        "language": "English",
    },
    {
        "id": 3,
        "title": "Fluent Python",
        "author": "<PERSON>",
        "publisher": "O'Reilly Media",
        "published_date": "2021-06-15",
        "page_count": 792,
        "language": "English",
    },
    {
        "id": 4,
        "title": "Automate the Boring Stuff with Python",
        "author": "<PERSON> Sweigart",
        "publisher": "No Starch Press",
        "published_date": "2019-11-12",
        "page_count": 504,
        "language": "English",
    },
    {
        "id": 5,
        "title": "Python Crash Course",
        "author": "<PERSON>",
        "publisher": "No Starch Press",
        "published_date": "2020-05-03",
        "page_count": 544,
        "language": "English",
    },
    {
        "id": 6,
        "title": "Effective Python",
        "author": "Brett Slatkin",
        "publisher": "Pearson",
        "published_date": "2019-07-17",
        "page_count": 416,
        "language": "English",
    },
    {
        "id": 7,
        "title": "Learning Python",
        "author": "Mark Lutz",
        "publisher": "O'Reilly Media",
        "published_date": "2018-07-06",
        "page_count": 1648,
        "language": "English",
    },
    {
        "id": 8,
        "title": "Python Tricks",
        "author": "Dan Bader",
        "publisher": "Dan Bader",
        "published_date": "2017-10-01",
        "page_count": 302,
        "language": "English",
    },
]
